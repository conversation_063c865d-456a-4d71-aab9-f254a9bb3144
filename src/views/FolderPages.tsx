import { useQueryClient } from "@tanstack/react-query";
import React from "react";
import { createNewPage, deletePage, renamePage, getDrawData } from "../db/draw";
import { Card, CardContent, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import Loader from "@/components/Loader";
import NoData from "./NoData";
import { Button } from "@/components/ui/button";
import dayjs from "dayjs";
import { useNavigate } from "@tanstack/react-router";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Trash2, MoreHorizontal, Edit, Copy, FileText, Search, X } from "lucide-react";
import { Input } from "@/components/ui/input";

import { useFolderPages, useFolders } from "@/hooks/useFolders";
import { useFolderContext } from "@/contexts/FolderContext";

interface DrawingCardProps {
  page: any;
  onNavigate: (pageId: string) => void;
  onDelete: (pageId: string) => void;
  onRename?: (pageId: string, newName: string) => void;
  onDuplicate?: (pageId: string) => void;
}

function DrawingCard({ page, onNavigate, onDelete, onRename, onDuplicate }: DrawingCardProps) {
  const [isRenaming, setIsRenaming] = React.useState(false);
  const [newName, setNewName] = React.useState(page.name || "Untitled");

  const handleRename = () => {
    if (newName.trim() && newName !== (page.name || "Untitled") && onRename) {
      onRename(page.page_id, newName.trim());
    }
    setIsRenaming(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleRename();
    } else if (e.key === 'Escape') {
      setNewName(page.name || "Untitled");
      setIsRenaming(false);
    }
  };

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't navigate if clicking on action buttons or input
    if (e.target instanceof HTMLElement) {
      if (e.target.closest('[data-action]') || e.target.tagName === 'INPUT') {
        return;
      }
    }
    onNavigate(page.page_id);
  };

  return (
    <Card className="group relative w-full max-w-sm cursor-pointer transition-all duration-200 hover:bg-background-hover border-border-subtle bg-background-card">
      <div onClick={handleCardClick} className="p-6">
        {/* Drawing Preview Area */}
        <div className="mb-4 h-32 w-full rounded-md bg-background-main border border-border-subtle flex items-center justify-center">
          <FileText className="h-8 w-8 text-text-muted" />
        </div>

        {/* Drawing Info */}
        <div className="space-y-2">
          {isRenaming ? (
            <input
              type="text"
              value={newName}
              onChange={(e) => setNewName(e.target.value)}
              onBlur={handleRename}
              onKeyDown={handleKeyPress}
              className="w-full px-2 py-1 text-sm bg-background-input border border-border-input rounded text-text-primary focus:outline-none focus:ring-1 focus:ring-accent-blue"
              autoFocus
              data-action="rename"
            />
          ) : (
            <h3 className="font-medium text-text-primary text-left truncate">
              {page.name || "Untitled"}
            </h3>
          )}

          <p className="text-xs text-text-muted text-left">
            Last updated {dayjs(page.updated_at).fromNow()}
          </p>
        </div>
      </div>

      {/* Action Menu */}
      <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity" data-action="menu">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 bg-background-card/80 backdrop-blur-sm hover:bg-background-hover"
              data-action="menu-trigger"
            >
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem onClick={() => setIsRenaming(true)}>
              <Edit className="mr-2 h-4 w-4" />
              Rename
            </DropdownMenuItem>
            {onDuplicate && (
              <DropdownMenuItem onClick={() => onDuplicate(page.page_id)}>
                <Copy className="mr-2 h-4 w-4" />
                Duplicate
              </DropdownMenuItem>
            )}
            <DropdownMenuItem
              onClick={() => {
                if (confirm(`Are you sure you want to delete "${page.name || 'Untitled'}"? This action cannot be undone.`)) {
                  onDelete(page.page_id);
                }
              }}
              className="text-red-400 focus:text-red-400"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </Card>
  );
}

interface EmptyFolderStateProps {
  folderName: string;
  onCreateDrawing: () => void;
  onCreateMermaidDrawing: () => void;
}

function EmptyFolderState({ folderName, onCreateDrawing, onCreateMermaidDrawing }: EmptyFolderStateProps) {
  return (
    <div className="flex flex-col items-center justify-center py-16 px-8 text-center">
      <div className="mb-6">
        <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-background-card border border-border-subtle flex items-center justify-center">
          <FileText className="h-8 w-8 text-text-muted" />
        </div>
        <h3 className="text-lg font-medium text-text-primary mb-2">
          No drawings in {folderName}
        </h3>
        <p className="text-text-muted max-w-md">
          Get started by creating your first drawing. You can create a blank canvas or start with a Mermaid diagram.
        </p>
      </div>

      <div className="flex flex-col sm:flex-row gap-3">
        <Button
          onClick={onCreateDrawing}
          className="font-medium"
        >
          <FileText className="mr-2 h-4 w-4" />
          Create Drawing
        </Button>
        <Button
          variant="outline"
          onClick={onCreateMermaidDrawing}
          className="font-medium"
        >
          Create Mermaid Diagram
        </Button>
      </div>
    </div>
  );
}

interface FolderHeaderProps {
  folderName: string;
  drawingCount: number;
  filteredCount: number;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onCreateDrawing: () => void;
  onCreateMermaidDrawing: () => void;
}

function FolderHeader({
  folderName,
  drawingCount,
  filteredCount,
  searchQuery,
  onSearchChange,
  onCreateDrawing,
  onCreateMermaidDrawing
}: FolderHeaderProps) {
  const [isSearchFocused, setIsSearchFocused] = React.useState(false);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onSearchChange('');
    }
  };

  // Global keyboard shortcuts
  React.useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      // Focus search on Ctrl/Cmd + K
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        const searchInput = document.querySelector('input[placeholder="Search drawings..."]') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
        }
      }
    };

    document.addEventListener('keydown', handleGlobalKeyDown);
    return () => document.removeEventListener('keydown', handleGlobalKeyDown);
  }, []);

  return (
    <div className="border-b border-border-subtle bg-background-main">
      <div className="px-8 py-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex flex-col">
            <h1 className="text-2xl font-semibold text-text-primary text-left font-sans">
              {folderName}
            </h1>
            <p className="text-text-muted text-left mt-1">
              {searchQuery ? `${filteredCount} of ${drawingCount}` : drawingCount} drawing{drawingCount !== 1 ? 's' : ''}
            </p>
          </div>

          <div className="flex items-center gap-3">
            <NewDrawingOptionDropdown
              createDrawingFn={onCreateDrawing}
              createMermaidDrawingFn={onCreateMermaidDrawing}
            />
          </div>
        </div>

        {/* Search Bar */}
        <div className="relative max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-text-muted" />
            <Input
              type="text"
              placeholder="Search drawings... (⌘K)"
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={() => setIsSearchFocused(true)}
              onBlur={() => setIsSearchFocused(false)}
              className="pl-10 pr-10 bg-background-input border-border-input focus:border-accent-blue"
              aria-label="Search drawings"
            />
            {searchQuery && (
              <button
                onClick={() => onSearchChange('')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-text-muted hover:text-text-primary transition-colors"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>
          {isSearchFocused && (
            <div className="absolute top-full mt-1 text-xs text-text-muted">
              Press <kbd className="px-1.5 py-0.5 bg-background-hover rounded text-text-muted border border-border-subtle">Esc</kbd> to clear
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

function DrawingCardSkeleton() {
  return (
    <Card className="w-full max-w-sm">
      <div className="p-6">
        {/* Drawing Preview Skeleton */}
        <div className="mb-4 h-32 w-full rounded-md bg-background-hover animate-pulse" />

        {/* Drawing Info Skeleton */}
        <div className="space-y-2">
          <div className="h-5 bg-background-hover rounded animate-pulse w-3/4" />
          <div className="h-3 bg-background-hover rounded animate-pulse w-1/2" />
        </div>
      </div>
    </Card>
  );
}

function DrawingsGridSkeleton() {
  return (
    <div className="px-8 py-6">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
        {Array.from({ length: 8 }).map((_, index) => (
          <DrawingCardSkeleton key={index} />
        ))}
      </div>
    </div>
  );
}

interface DrawingsGridProps {
  children: React.ReactNode;
}

function DrawingsGrid({ children }: DrawingsGridProps) {
  return (
    <div className="px-8 py-6">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
        {children}
      </div>
    </div>
  );
}

interface ErrorStateProps {
  title: string;
  message: string;
  onRetry?: () => void;
  showRetry?: boolean;
}

function ErrorState({ title, message, onRetry, showRetry = true }: ErrorStateProps) {
  return (
    <div className="h-full w-full flex items-center justify-center">
      <div className="text-center max-w-md">
        <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-500/10 border border-red-500/20 flex items-center justify-center">
          <X className="h-8 w-8 text-red-400" />
        </div>
        <h2 className="text-lg font-medium text-text-primary mb-2">{title}</h2>
        <p className="text-text-muted mb-6">{message}</p>
        {showRetry && onRetry && (
          <Button onClick={onRetry} variant="outline" className="font-medium">
            Try again
          </Button>
        )}
      </div>
    </div>
  );
}

function NewDrawingOptionDropdown({
  createDrawingFn,
  createMermaidDrawingFn,
}: {
  createDrawingFn: () => void;
  createMermaidDrawingFn: () => void;
}) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="default" className="font-medium text-sm">
          + New Drawing
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={createDrawingFn}>Plain Drawing</DropdownMenuItem>
        <DropdownMenuItem onClick={createMermaidDrawingFn}>
          Mermaid Syntax Diagram
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

interface FolderPagesProps {
  folderId: string;
}

export default function FolderPages({ folderId }: FolderPagesProps) {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { folders, isLoading: foldersLoading } = useFolders();
  const { pages, isLoading: pagesLoading } = useFolderPages(folderId);
  const { setSelectedFolderId } = useFolderContext();
  const [searchQuery, setSearchQuery] = React.useState('');

  const selectedFolder = folders?.find(f => f.folder_id === folderId);

  // Filter pages based on search query
  const filteredPages = React.useMemo(() => {
    if (!pages || !searchQuery.trim()) return pages;

    const query = searchQuery.toLowerCase().trim();
    return pages.filter(page =>
      (page.name || 'Untitled').toLowerCase().includes(query)
    );
  }, [pages, searchQuery]);

  // Retry functions
  const retryFolders = () => {
    queryClient.invalidateQueries({ queryKey: ["folders"] });
  };

  const retryPages = () => {
    queryClient.invalidateQueries({ queryKey: ["folderPages", folderId] });
  };

  // Set the selected folder ID when the component mounts or folderId changes
  React.useEffect(() => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] FolderPages - Setting selected folder ID:`, { folderId });
    if (folderId) {
      setSelectedFolderId(folderId);
    }
  }, [folderId, setSelectedFolderId]);

  // Loading states
  if (foldersLoading) {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] FolderPages - Showing loader (folders loading)`);
    return (
      <div className="h-full w-full flex flex-col bg-background-main">
        <div className="border-b border-border-subtle bg-background-main">
          <div className="px-8 py-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex flex-col">
                <div className="h-8 bg-background-hover rounded animate-pulse w-48 mb-2" />
                <div className="h-4 bg-background-hover rounded animate-pulse w-32" />
              </div>
              <div className="h-9 bg-background-hover rounded animate-pulse w-32" />
            </div>
            <div className="h-9 bg-background-hover rounded animate-pulse w-64" />
          </div>
        </div>
        <div className="flex-1">
          <DrawingsGridSkeleton />
        </div>
      </div>
    );
  }

  // Error states
  if (!foldersLoading && !selectedFolder) {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] FolderPages - Showing folder not found error`, {
      folderId,
      availableFolders: folders?.map(f => ({ id: f.folder_id, name: f.name }))
    });
    return (
      <ErrorState
        title="Folder not found"
        message="The folder you're looking for doesn't exist or has been deleted."
        onRetry={retryFolders}
      />
    );
  }

  // Navigation functions
  function goToPage(id: string) {
    navigate({ to: "/page/$id", params: { id: id } });
  }

  // Drawing management functions
  async function createDrawing() {
    if (!folderId) return;

    const data = await createNewPage(undefined, folderId);

    if (data.data && data.data[0]?.page_id) {
      // Invalidate caches to update sidebar immediately
      queryClient.invalidateQueries({ queryKey: ["pages"] });
      queryClient.invalidateQueries({ queryKey: ["folderPages"] });
      goToPage(data.data[0].page_id);
      toast("Successfully created a new drawing!");
    }

    if (data.error) {
      toast("An error occurred", {
        description: `Error: ${data.error.message}`,
      });
    }
  }

  async function createMermaidDrawing() {
    navigate({ to: "/mermaid" });
  }

  async function handleDrawingDelete(pageId: string) {
    const data = await deletePage(pageId);

    if (data.error === null) {
      // Invalidate caches to update sidebar immediately
      queryClient.invalidateQueries({ queryKey: ["pages"] });
      queryClient.invalidateQueries({ queryKey: ["folderPages"] });
      toast("Successfully deleted the drawing!");
    }

    if (data.error) {
      toast("An error occurred", {
        description: `Error: ${data.error.message}`,
      });
    }
  }

  async function handleDrawingRename(pageId: string, newName: string) {
    const data = await renamePage(pageId, newName);

    if (data.error === null) {
      // Invalidate caches to update UI immediately
      queryClient.invalidateQueries({ queryKey: ["pages"] });
      queryClient.invalidateQueries({ queryKey: ["folderPages"] });
      toast("Successfully renamed the drawing!");
    }

    if (data.error) {
      toast("An error occurred", {
        description: `Error: ${data.error.message}`,
      });
    }
  }

  async function handleDrawingDuplicate(pageId: string) {
    try {
      // First, get the original page data
      const originalPageData = await getDrawData(pageId);

      if (originalPageData.error || !originalPageData.data || originalPageData.data.length === 0) {
        toast("An error occurred", {
          description: "Could not retrieve original drawing data",
        });
        return;
      }

      const originalPage = originalPageData.data[0];
      const originalElements = originalPage.page_elements?.elements || [];
      const originalName = originalPage.name || "Untitled";

      // Create a new page with the same elements
      const duplicateData = await createNewPage(originalElements, folderId);

      if (duplicateData.data && duplicateData.data[0]?.page_id) {
        // Rename the duplicate to indicate it's a copy
        const copyName = `${originalName} (Copy)`;
        await renamePage(duplicateData.data[0].page_id, copyName);

        // Invalidate caches to update UI immediately
        queryClient.invalidateQueries({ queryKey: ["pages"] });
        queryClient.invalidateQueries({ queryKey: ["folderPages"] });
        toast("Successfully duplicated the drawing!");
      }

      if (duplicateData.error) {
        toast("An error occurred", {
          description: `Error: ${duplicateData.error.message}`,
        });
      }
    } catch (error) {
      toast("An error occurred", {
        description: "Failed to duplicate drawing",
      });
    }
  }

  const drawingCount = pages?.length || 0;
  const filteredCount = filteredPages?.length || 0;
  const folderName = selectedFolder?.name || "Unknown Folder";

  // Main render - borderless full-screen layout
  return (
    <div className="h-full w-full flex flex-col bg-background-main">
      {/* Folder Header */}
      <FolderHeader
        folderName={folderName}
        drawingCount={drawingCount}
        filteredCount={filteredCount}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        onCreateDrawing={createDrawing}
        onCreateMermaidDrawing={createMermaidDrawing}
      />

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto">
        {pagesLoading ? (
          <DrawingsGridSkeleton />
        ) : filteredPages && filteredPages.length > 0 ? (
          <DrawingsGrid>
            {filteredPages.map((page) => (
              <DrawingCard
                key={page.page_id}
                page={page}
                onNavigate={goToPage}
                onDelete={handleDrawingDelete}
                onRename={handleDrawingRename}
                onDuplicate={handleDrawingDuplicate}
              />
            ))}
          </DrawingsGrid>
        ) : searchQuery ? (
          <div className="flex flex-col items-center justify-center py-16 px-8 text-center">
            <div className="mb-6">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-background-card border border-border-subtle flex items-center justify-center">
                <Search className="h-8 w-8 text-text-muted" />
              </div>
              <h3 className="text-lg font-medium text-text-primary mb-2">
                No drawings found
              </h3>
              <p className="text-text-muted max-w-md">
                No drawings match your search for "{searchQuery}". Try a different search term or create a new drawing.
              </p>
            </div>
            <Button
              onClick={() => setSearchQuery('')}
              variant="outline"
              className="font-medium"
            >
              Clear search
            </Button>
          </div>
        ) : (
          <EmptyFolderState
            folderName={folderName}
            onCreateDrawing={createDrawing}
            onCreateMermaidDrawing={createMermaidDrawing}
          />
        )}
      </div>
    </div>
  );
}
