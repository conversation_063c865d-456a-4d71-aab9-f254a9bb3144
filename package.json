{"name": "draw", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@excalidraw/excalidraw": "^0.18.0", "@excalidraw/mermaid-to-excalidraw": "^1.1.2", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@sentry/react": "^8.42.0", "@supabase/supabase-js": "^2.47.2", "@tanstack/react-query": "^5.62.3", "@tanstack/react-router": "^1.87.0", "bun": "^1.1.42", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "dayjs": "^1.11.13", "hono": "^4.6.14", "lucide-react": "^0.511.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8", "zustand": "^4.5.5"}, "devDependencies": {"@eslint/js": "^9.16.0", "@tanstack/eslint-plugin-query": "^5.62.1", "@tanstack/router-plugin": "^1.86.0", "@types/bun": "^1.1.14", "@types/node": "^22.10.1", "@types/react": "^18.3.14", "@types/react-dom": "^18.3.2", "@vitejs/plugin-react-swc": "^3.7.2", "autoprefixer": "^10.4.20", "eslint": "^9.16.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.13.0", "install": "^0.13.0", "postcss": "^8.4.49", "prettier": "3.3.3", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^3.4.16", "typescript": "^5.7.2", "typescript-eslint": "^8.17.0", "vite": "^5.4.11"}}